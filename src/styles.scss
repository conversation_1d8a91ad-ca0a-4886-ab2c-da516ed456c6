.acc-7821 {
  .table {
    border-collapse: separate;
    border-spacing: 0;
    thead.cc-secondary-background {
      th {
        padding: 12px 8px;
        font-weight: 500;
        text-align: center;
        vertical-align: middle;
        border-bottom: 2px solid rgba(0, 0, 0, 0.1);
        white-space: nowrap;

        &.text-white {
          color: white !important;
        }
      }
    }
    tbody {
      tr {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.03);
        }
      }

      td {
        padding: 10px 8px;
        vertical-align: middle;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &.text-center {
          text-align: center;
        }
      }
    }
    cc-select,
    cc-input,
    cc-datepicker,
    cc-checkbox,
    cc-radio-group {
      width: 100%;
      min-width: 100%;
      max-width: 100%;
      display: block;
    }
    colgroup {
      col {
        &[style*="width: 5%"] {
          width: 5% !important;
        }
        &[style*="width: 8%"] {
          width: 8% !important;
        }
        &[style*="width: 10%"] {
          width: 10% !important;
        }
        &[style*="width: 12%"] {
          width: 12% !important;
        }
        &[style*="width: 15%"] {
          width: 15% !important;
        }
        &[style*="width: 20%"] {
          width: 20% !important;
        }
        &[style*="width: 25%"] {
          width: 25% !important;
        }
      }
    }
  }
  .table-responsive {
    position: relative;
  
    table {
      margin-bottom: 0;
    }
  
    thead {
      position: sticky;
      top: 0;
      z-index: 1;
    }
  
    th {
      position: sticky;
      top: 0;
      background-color: var(--cc-secondary-color);
    }
  }
  
}
