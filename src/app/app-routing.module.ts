import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './modules/core/components/login/login.component';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  {
    path: 'payments/add-for-approval/:id/:contractId',
    loadChildren: () =>
      import(
        './modules/add-payment-for-approval/add-payment-for-approval.module'
      ).then((m) => m.AddPaymentForApprovalModule),
    data: { label: '' },
  },
];
@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      onSameUrlNavigation: 'reload',
      useHash: false,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
