import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddPaymentForApprovalComponent } from './components/add-payment-for-approval/add-payment-for-approval.component';

const routes: Routes = [
  {
    path: '',
    component: AddPaymentForApprovalComponent,
    data: { pageCode: 'AddClientPaymentsForApproval'},
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AddPaymentForApprovalRoutingModule {}
