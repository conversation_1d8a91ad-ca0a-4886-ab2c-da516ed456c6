import {AfterViewInit, Component, <PERSON>ementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {AddPaymentForApprovalsService} from '../../services/add-payment-for-approvals.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {CCNotificationService} from '@maids/cc-lib/services';
import {PaginationRequest} from '@maids/cc-lib/common';
import {Observable, Subject} from 'rxjs';
import {CCDialog} from '@maids/cc-lib/dialog';
import {DuplicatedDialogComponent} from '../duplicated-dialog/duplicated-dialog.component';
import {CCFileUploaderConfig} from '@maids/cc-lib/file-uploader';
import {BreadcrumbLink} from "@maids/cc-lib/layout";

@Component({
    selector: 'app-add-payment-for-approval',
    templateUrl: './add-payment-for-approval.component.html',
    styleUrls: ['./add-payment-for-approval.component.scss'],
})
export class AddPaymentForApprovalComponent implements OnInit, OnDestroy, AfterViewInit {

    @ViewChild('breadcrumbsComponent', { static: false }) breadcrumbsComponent!: ElementRef;

    breadcrumbs1: BreadcrumbLink[] = [
        {
            label: "Family Payments", url: "client/payments/" +
                this.route.snapshot.params['id'] + "/" +
                this.route.snapshot.params['contractId']
        },
        {label: "Add Payment For Approval", disabled: true},
    ];

    form = this.fb.group({
        replacementOfBouncedPayment: [false],
        methodOfPaymentSelect: [null],
        payingOnline: [false],
        flagAsPayingViaCC: [false],
        transferReference: [null],
        paymentDate: [null],
        receiptAttachment: [null],
        description: [null],
        existingTypeFilter: [null],
        amount: [0],
        discountAmount: [0],
        required: [false],
    });
    clientEnchanterTodoId: string = '';
    allTypeFilter: any;
    bouncedPaymentId: any = '';
    clientTodoInfo: any;
    requiredPayments: { [key: string]: boolean } = {};
    currentClientID: string = '';
    currentContractId: string = '';
    replaceOf: any;
    disableDiscount: boolean = true;
    client: any;
    numberOfProrated: any;
    numberOfInitial: any;
    allowedInitialPayments: any;
    allowedProRatedPayments: any;
    payingViaCreditCard: boolean = false;
    flagAsPayingViaCC: boolean = false;
    showWarningMessageForIPAM: boolean = false;
    popupWarningMessageDaysInIpamFlow: number = 0;
    allowedWaivedPayments: any;
    contractProspectType: any;
    workerSalary: any;
    contractObj: any;
    bouncedPayments: any[] = [];
    paymentDetails: any;
    tempBouncedPayments: any[] = [];
    tempFuturePayments: any[] = [];
    hideReplaceBounced: any = '';
    differenceThreshold: any;
    private destroy$ = new Subject<void>();
    private paymentWatcherIntervals: any[] = [];
    config: CCFileUploaderConfig = {
        maxFilesize: 10,
    };
    newPayment: any[] = [];
    futurePayments: any[] = [];
    allPayments: any[] = [];
    diff: any;
    signedDiff: any;
    showWarningMsg: any;
    isLess: any;
    vat: any;
    dataToConfirm: any;
    paymentType: any;
    filteredFuturePayments: any[] = [];
    duplicatedPayment: any = {
        message: '',
        url: '',
        todoUuid: '',
        todoId: '',
    };
    dataMatched: any;
    rowCount: number = 1;
    proratedCount: number = 0;
    initialCount: number = 0;
    disableRequired: boolean = false;
    methodOfPaymentOptions: any[] = [
        {id: 'CASH', text: 'Cash'},
        {id: 'CARD', text: 'Card'},
        {id: 'WIRE_TRANSFER', text: 'Wire Transfer'},
    ];

    readonly typeOfPaymentOptions = (
        pageReq: PaginationRequest
    ): Observable<any> => {
        return this.addPaymentForApprovalsService.getTypeOfPayment(
            pageReq.page,
            pageReq.size,
            pageReq.searchString
        );
    };
    readonly subTypeOfPaymentOptions = (
        pageReq: PaginationRequest
    ): Observable<any> => {
        return this.addPaymentForApprovalsService.APFApaymentSubType(
            pageReq.page,
            pageReq.size,
            pageReq.searchString || ''
        );
    };

    constructor(
        private addPaymentForApprovalsService: AddPaymentForApprovalsService,
        private fb: FormBuilder,
        private route: ActivatedRoute,
        public readonly notifications: CCNotificationService,
        private ccDialog: CCDialog,
        private router: Router
    ) {
    }

    ngOnInit(): void {
        this.currentClientID = this.route.snapshot.params['id'];
        this.currentContractId = this.route.snapshot.params['contractId'];
        this.replaceOf = this.route.snapshot.params['replaceOf'];
        this.clientEnchanterTodoId =
            this.route.snapshot.queryParams['clientEnchanterTodoId'];

        // Initialize the newPayment array
        this.newPayment = [];

        if (this.clientEnchanterTodoId) {
            this.getAccountingClientenChanterTodoExtraInfo();
        }
        this.getClient();
        this.getInitialProratedNumbers();
        this.getAllowedInitialProrated();
        this.getContractDetails();
        this.checkDisableDiscount();
        this.getFuturePayments();
        if (this.replaceOf) {
            this.createTypeOfPayment();
            this.methodOfPaymentOptions = [
                {id: 'CASH', text: 'Cash'},
                {id: 'WIRE_TRANSFER', text: 'Wire Transfer'},
                {id: 'BANK_DEPOSIT', text: 'Bank deposit'},
                {id: 'CARD', text: 'Card'},
                {id: 'CHEQUE', text: 'Cheque'},
                {id: 'ADJUSTMENT', text: 'Adjustment'},
                {id: 'DIRECT_DEBIT', text: 'Direct Debit'},
            ];
        } else {
            this.getBouncedPayments();
        }
        this.addPaymentForApprovalsService
            .getParameterValue('wire_transfer_amount_difference_threshold')
            .subscribe({
                next: (res: any) => {
                    this.differenceThreshold = parseInt(res[0].value);
                },
            });

        // Set up form control watchers (equivalent to $scope.$watch)
        this.setupFormWatchers();
    }

    ngAfterViewInit(): void {
        // Hide the breadcrumbs-container div inside cc-breadcrumbs
        if (this.breadcrumbsComponent) {
            const breadcrumbsContainer = this.breadcrumbsComponent.nativeElement.querySelector('.breadcrumbs-container');
            if (breadcrumbsContainer) {
                breadcrumbsContainer.style.display = 'none';
            }
        }
    }

    private populateDuplicatedPayment(data: any): void {
        this.duplicatedPayment = {
            message: data?.message || '',
            url: data?.url || '',
            todoUuid: data?.todoUuid || '',
            todoId: data?.todoId || '',
            hideTriggerReminderFlow: data?.hideTriggerReminderFlow || false,
        };
    }

    openDuplicatedDialog(data: any): void {
        this.populateDuplicatedPayment(data);
        this.ccDialog
            .originalOpen(DuplicatedDialogComponent, {data: this.duplicatedPayment})
            .afterClosed()
            .subscribe((event: any) => {
                switch (event) {
                    case 'sendSMS':
                        this.sendDuplicatedSMS();
                        break;
                    case 'copyURL':
                        this.copyDuplicatedURL();
                        break;
                    case 'triggerReminderFlow':
                        this.triggerReminderFlow();
                        break;
                    default:
                        break;
                }
            });
    }

    copyDuplicatedURL(): void {
        if (!this.duplicatedPayment?.todoUuid) {
            this.notifications.notifyError('Missing todo UUID.');
            return;
        }
        this.setReactivationPaymentForTodo();
        this.copy(this.duplicatedPayment?.url || '');
    }

    sendDuplicatedSMS(): void {
        if (!this.duplicatedPayment?.todoUuid) {
            this.notifications.notifyError('Missing todo UUID.');
            return;
        }
        this.addPaymentForApprovalsService
            .sendPayTabsLinkViaMessage(this.duplicatedPayment.todoUuid)
            .subscribe({
                next: () => {
                    this.notifications.notifySuccess('SMS Sent successfully');
                },
            });
    }

    triggerReminderFlow(): void {
        this.checkMatchedToDoIfRelatedToRunningFlow();
    }

    /**
     * Sets up watchers for form controls and other properties
     * This is equivalent to $scope.$watch in AngularJS
     */
    onPaymentTypeChange(event: any): void {
        // If nothing is selected (event is null, undefined, or empty), return all payments
        if (!event || event === null || event === undefined || event === '') {
            this.filteredFuturePayments = this.futurePayments;
            return;
        }

        // Filter futurePayments based on event matching typeOfPayment.id
        const filteredPayments = this.futurePayments.filter(
            (payment) => payment.typeOfPayment.id == event
        );

        this.filteredFuturePayments = filteredPayments;
    }

    setupFormWatchers(): void {
        // Watch for changes in methodOfPaymentSelect
        this.form.get('methodOfPaymentSelect')?.valueChanges.subscribe((newVal) => {
            this.updateAmount();

            if (newVal === 'CARD') {
                this.form.get('payingOnline')?.setValue(true);
                this.form.get('required')?.setValue(false);
                this.updateAmount();
            }

            if (newVal !== 'CARD') {
                this.form.get('payingOnline')?.setValue(false);
                this.form.get('flagAsPayingViaCC')?.setValue(false);
                this.form.get('required')?.setValue(false);
            }

            if (newVal !== 'WIRE_TRANSFER') {
                this.form.get('transferReference')?.setValue('');
            }
        });

        // Watch for changes in flagAsPayingViaCC
        this.form.get('flagAsPayingViaCC')?.valueChanges.subscribe((newVal) => {
            if (newVal === true) {
                this.form.get('required')?.setValue(false);
                this.disableRequired = true;
                this.form.get('payingOnline')?.setValue(true);
                this.getPayingViaCreditCardPayment();
            } else {
                this.disableRequired = false;
            }
        });

        // Watch for changes in replacementOfBouncedPayment
        this.form
            .get('replacementOfBouncedPayment')
            ?.valueChanges.subscribe((newVal) => {
            this.requiredPayments = {};
            if (newVal) {
                this.getBouncedPayments();
            }
        });

        // Watch for changes in payingOnline
        this.form.get('payingOnline')?.valueChanges.subscribe((newVal) => {
            if (newVal) {
                this.form.get('receiptAttachment')?.setValue('');
            }
        });

        // Set up watchers for bounced payments and future payments arrays
        this.setupArrayWatchers();
    }

    /**
     * Sets up watchers for the bouncedPayments and futurePayments arrays
     */
    setupArrayWatchers(): void {
        // Watch for changes in bouncedPayments
        const bouncedPaymentsInterval = setInterval(() => {
            this.updateAmount();
        }, 500);
        this.paymentWatcherIntervals.push(bouncedPaymentsInterval);

        // Watch for changes in futurePayments
        const futurePaymentsInterval = setInterval(() => {
            this.updateAmount();
        }, 500);
        this.paymentWatcherIntervals.push(futurePaymentsInterval);
    }

    /**
     * Handles changes in the newPayment array
     * This is called whenever a payment property changes
     */
    handleNewPaymentChanges(
        index: number,
        property: string,
        _newValue: any,
        oldValue: any
    ): void {
        const payment = this.newPayment[index];

        // Handle payment type changes
        if (
            property === 'paymentType' &&
            payment.paymentType &&
            payment.paymentType !== oldValue
        ) {
            if (payment.paymentType.code !== 'monthly_payment') {
                payment.prorated = false;
            }
            payment.amountOfPayment = 0;
            payment.additionsDiscounts = 'discounts';
            this.getPaymentDiscountInfo(index, payment.paymentType.id);
        }

        // Handle payment date changes
        if (
            property === 'paymentDate' &&
            payment.paymentDate &&
            payment.paymentType &&
            payment.paymentDate !== oldValue
        ) {
            payment.amountOfPayment = 0;
            payment.additionsDiscounts = 'discounts';
            this.getPaymentDiscountInfo(index, payment.paymentType.id);
        }

        // Handle additions/discounts changes
        if (
            property === 'additionsDiscounts' &&
            payment.additionsDiscounts !== oldValue
        ) {
            this.handleAdditionsDiscountsChange(payment);
        }

        // Handle addition amount changes
        if (property === 'additionAmount' && payment.additionAmount !== oldValue) {
            this.handleAdditionAmountChange(payment);
        }

        // Handle additional discount amount changes
        if (
            property === 'additionalDiscountAmount' &&
            payment.additionalDiscountAmount !== oldValue
        ) {
            this.handleAdditionalDiscountAmountChange(payment);
        }

        // Handle amount of payment changes
        if (
            property === 'amountOfPayment' &&
            payment.amountOfPayment !== oldValue
        ) {
            this.handleAmountOfPaymentChange(payment);
        }

        // Handle affected by credit note changes
        if (
            property === 'affectedByCreditNote' &&
            payment.affectedByCreditNote !== oldValue
        ) {
            this.handleAffectedByCreditNoteChange(payment);
        }

        // Handle include worker salary changes
        if (
            property === 'includeWorkerSalary' &&
            payment.includeWorkerSalary !== oldValue
        ) {
            this.handleIncludeWorkerSalaryChange(payment);
        }

        // Handle prorated changes
        if (property === 'prorated' && payment.prorated !== oldValue) {
            this.handleProratedChange(payment, index);
        }

        // Update the amount after any change
        setTimeout(() => {
            this.updateAmount();
        }, 100);
    }

    /**
     * Handles changes to the additions/discounts property
     */
    handleAdditionsDiscountsChange(payment: any): void {
        if (payment.additionsDiscounts === 'additions') {
            payment.affectedByCreditNote = false;
            payment.disableMoreAdditionalDiscount = true;
            if (!isNaN(payment.additionAmount)) {
                payment.amendedAmount = this.calcAdd(payment);
                if (payment.affectedByCreditNote) {
                    payment.affectedByAdditionalDiscount = false;
                    payment.finalAmount = this.addSalary(
                        Math.round(
                            (payment.amendedAmount / this.vat -
                                payment.moreAdditionalDiscount) *
                            this.vat
                        ),
                        payment.includeWorkerSalary
                    );
                } else {
                    payment.finalAmount = this.addSalary(
                        payment.amendedAmount,
                        payment.includeWorkerSalary
                    );
                }
            }
        }

        if (payment.additionsDiscounts === 'discounts') {
            if (payment.moreAdditionalDiscount > 0) {
                payment.disableMoreAdditionalDiscount = false;
            }
            if (!isNaN(payment.additionalDiscountAmount)) {
                payment.amendedAmount = this.calcDis(payment);
                if (payment.affectedByCreditNote) {
                    payment.affectedByAdditionalDiscount = false;
                    payment.finalAmount = this.addSalary(
                        Math.round(
                            (payment.amendedAmount / this.vat -
                                payment.moreAdditionalDiscount) *
                            this.vat
                        ),
                        payment.includeWorkerSalary
                    );
                } else {
                    payment.finalAmount = this.addSalary(
                        payment.amendedAmount,
                        payment.includeWorkerSalary
                    );
                }
            }
        }
    }

    /**
     * Handles changes to the addition amount property
     */
    handleAdditionAmountChange(payment: any): void {
        if (payment.additionsDiscounts) {
            if (payment.additionsDiscounts === 'additions') {
                payment.affectedByAdditionalDiscount = false;
                payment.additionalDiscountAmount = '';
                if (!isNaN(payment.additionAmount)) {
                    payment.amendedAmount = this.calcAdd(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
            if (payment.additionsDiscounts === 'discounts') {
                payment.additionAmount = '';
                payment.affectedByAdditionalDiscount = false;
                if (!isNaN(payment.additionalDiscountAmount)) {
                    payment.amendedAmount = this.calcDis(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
        }
    }

    /**
     * Handles changes to the additional discount amount property
     */
    handleAdditionalDiscountAmountChange(payment: any): void {
        if (payment.additionsDiscounts) {
            if (payment.additionsDiscounts === 'additions') {
                payment.additionalDiscountAmount = '';
                payment.affectedByAdditionalDiscount = false;
                if (!isNaN(payment.additionAmount)) {
                    payment.amendedAmount = this.calcAdd(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
            if (payment.additionsDiscounts === 'discounts') {
                payment.additionAmount = '';
                payment.affectedByAdditionalDiscount = false;
                if (!isNaN(payment.additionalDiscountAmount)) {
                    payment.amendedAmount = this.calcDis(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
        }
    }

    /**
     * Handles changes to the amount of payment property
     */
    handleAmountOfPaymentChange(payment: any): void {
        if (!payment.additionsDiscounts) {
            payment.finalAmount = payment.amountOfPayment;
            if (payment.affectedByCreditNote) {
                payment.affectedByAdditionalDiscount = false;
                payment.finalAmount = Math.round(
                    (payment.amountOfPayment / this.vat -
                        payment.moreAdditionalDiscount) *
                    this.vat
                );
            }
        }
        if (payment.additionsDiscounts === 'additions') {
            if (!isNaN(payment.additionAmount)) {
                payment.amendedAmount = this.calcAdd(payment);
                if (payment.affectedByCreditNote) {
                    payment.affectedByAdditionalDiscount = false;
                    payment.finalAmount = this.addSalary(
                        Math.round(
                            (payment.amendedAmount / this.vat -
                                payment.moreAdditionalDiscount) *
                            this.vat
                        ),
                        payment.includeWorkerSalary
                    );
                } else {
                    payment.finalAmount = this.addSalary(
                        payment.amendedAmount,
                        payment.includeWorkerSalary
                    );
                }
            }
        }
        if (payment.additionsDiscounts === 'discounts') {
            if (!isNaN(payment.additionalDiscountAmount)) {
                payment.amendedAmount = this.calcDis(payment);
                if (payment.affectedByCreditNote) {
                    payment.affectedByAdditionalDiscount = false;
                    payment.finalAmount = this.addSalary(
                        Math.round(
                            (payment.amendedAmount / this.vat -
                                payment.moreAdditionalDiscount) *
                            this.vat
                        ),
                        payment.includeWorkerSalary
                    );
                } else {
                    payment.finalAmount = this.addSalary(
                        payment.amendedAmount,
                        payment.includeWorkerSalary
                    );
                }
            }
        }
    }

    /**
     * Handles changes to the affected by credit note property
     */
    handleAffectedByCreditNoteChange(payment: any): void {
        if (payment.additionsDiscounts) {
            if (payment.additionsDiscounts === 'additions') {
                if (!isNaN(payment.additionAmount)) {
                    payment.amendedAmount = this.calcAdd(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
            if (payment.additionsDiscounts === 'discounts') {
                if (!isNaN(payment.additionalDiscountAmount)) {
                    payment.amendedAmount = this.calcDis(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
        } else {
            if (payment.affectedByCreditNote) {
                payment.affectedByAdditionalDiscount = false;
                payment.finalAmount = Math.round(
                    (payment.amountOfPayment / this.vat -
                        payment.moreAdditionalDiscount) *
                    this.vat
                );
            } else {
                if (payment.prorated) {
                    payment.affectedByCreditNote = false;
                    payment.additionsDiscounts = '';
                    payment.disableAdditionsDiscounts = true;
                    payment.disableAmount = false;
                } else {
                    payment.finalAmount = payment.amountOfPayment;
                    if (payment.amountOfPayment > 0) {
                        payment.disableAmount = true;
                    }
                }
            }
        }
    }

    /**
     * Handles changes to the include worker salary property
     */
    handleIncludeWorkerSalaryChange(payment: any): void {
        if (payment.additionsDiscounts) {
            if (payment.additionsDiscounts === 'additions') {
                if (!isNaN(payment.additionAmount)) {
                    payment.amendedAmount = this.calcAdd(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
            if (payment.additionsDiscounts === 'discounts') {
                if (!isNaN(payment.additionalDiscountAmount)) {
                    payment.amendedAmount = this.calcDis(payment);
                    if (payment.affectedByCreditNote) {
                        payment.affectedByAdditionalDiscount = false;
                        payment.finalAmount = this.addSalary(
                            Math.round(
                                (payment.amendedAmount / this.vat -
                                    payment.moreAdditionalDiscount) *
                                this.vat
                            ),
                            payment.includeWorkerSalary
                        );
                    } else {
                        payment.finalAmount = this.addSalary(
                            payment.amendedAmount,
                            payment.includeWorkerSalary
                        );
                    }
                }
            }
        }
    }

    /**
     * Handles changes to the prorated property
     */
    handleProratedChange(payment: any, index: number): void {
        if (payment.prorated) {
            if (payment.paymentType) {
                this.getPaymentDiscountInfo(index, payment.paymentType.id);
            }
            payment.affectedByCreditNote = false;
            payment.disableAdditionsDiscounts = true;
            payment.disableAmount = false;
            payment.additionsDiscounts = '';
            payment.finalAmount = payment.amountOfPayment;
        } else {
            if (payment.paymentType) {
                this.getPaymentDiscountInfo(index, payment.paymentType.id);
            }
            payment.disableAdditionsDiscounts = false;
            if (payment.amountOfPayment > 0) {
                payment.disableAmount = true;
            }
        }
    }

    confirmCreate(data: any) {
        this.addPaymentForApprovalsService.confirmCreate(data).subscribe({
            next: () => {
                this.notifications.notifySuccess('Payment inserted successfully');
                this.cancelOperation();
            },
        });
    }

    getAccountingClientenChanterTodoExtraInfo() {
        this.addPaymentForApprovalsService
            .getAccountingClientenChanterTodoExtraInfo(this.clientEnchanterTodoId)
            .subscribe({
                next: (res: any) => {
                    if (res) {
                        this.clientTodoInfo = res;
                    }
                    if (res?.extraInfo?.bouncedPaymentId) {
                        this.bouncedPaymentId = res.extraInfo.bouncedPaymentId;
                        this.form.patchValue({
                            replacementOfBouncedPayment: true,
                        });
                    }
                },
            });
    }

    getClient() {
        this.addPaymentForApprovalsService
            .getClient(this.currentClientID)
            .subscribe({
                next: (res: any) => {
                    this.client = res;
                },
            });
    }

    getInitialProratedNumbers() {
        this.addPaymentForApprovalsService
            .getInitialProratedNumbers(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.numberOfProrated = res.numberOfProrated;
                    this.numberOfInitial = res.numberOfInitial;
                },
            });
    }

    getAllowedInitialProrated() {
        this.addPaymentForApprovalsService
            .getAllowedInitialProrated(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.allowedInitialPayments = res.allowedInitialPayments;
                    this.allowedProRatedPayments = res.allowedProRatedPayments;
                    this.payingViaCreditCard = res.payingViaCreditCard ? true : false;
                    this.form.controls['flagAsPayingViaCC'].setValue(
                        res.flagAsPayingViaCC ? true : false
                    );
                    this.showWarningMessageForIPAM = res.showWarningMessageForIPAM
                        ? true
                        : false;
                    if (this.payingViaCreditCard) {
                        this.form.controls['required'].setValue(false);
                        this.disableRequired = true;
                    }
                    if (this.showWarningMessageForIPAM) {
                        this.addPaymentForApprovalsService
                            .getParameterValue('popup_warning_message_days_in_ipam_flow')
                            .subscribe({
                                next: (paramres: any) => {
                                    this.popupWarningMessageDaysInIpamFlow = parseInt(
                                        paramres[0].value
                                    );
                                },
                            });
                    }

                    this.allowedWaivedPayments = res.allowedWaivedPayments;
                },
            });
    }

    getContractDetails() {
        this.addPaymentForApprovalsService
            .getContractDetails(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.contractProspectType = res.contractProspectType.code;
                    this.workerSalary = res.workerSalary || 0;
                    this.contractObj = res;
                },
            });
    }

    checkDisableDiscount() {
        this.addPaymentForApprovalsService
            .allowAddDiscountToPaymentForApproval()
            .subscribe({
                next: (response: boolean) => {
                    this.disableDiscount = !response;
                },
            });

        let oneSelected = false;
        if (
            this.form.value.replacementOfBouncedPayment &&
            this.bouncedPayments.length > 0
        ) {
            Object.keys(this.requiredPayments).forEach((key) => {
                if (this.requiredPayments[key]) {
                    oneSelected = true;
                }
            });
        }
        return oneSelected;
    }

    createTypeOfPayment() {
        this.addPaymentForApprovalsService.getAllTypeOfPayment().subscribe({
            next: () => {
                this.paymentDetails = this.addPaymentForApprovalsService
                    .getPaymentDetails(this.replaceOf)
                    .subscribe({
                        next: () => {
                            this.form.controls['replacementOfBouncedPayment'].setValue(true);
                        },
                    });
            },
        });
    }

    getBouncedPayments() {
        this.addPaymentForApprovalsService
            .getunreplacedbouncedpayments(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.requiredPayments = {};
                    this.bouncedPayments = res;
                    this.tempBouncedPayments = [...this.bouncedPayments];
                    this.hideReplaceBounced = res.length === 0;
                    if (this.replaceOf) {
                        this.requiredPayments[this.replaceOf] = true;
                    }
                    if (this.bouncedPaymentId) {
                        this.requiredPayments[this.bouncedPaymentId] = true;
                    }
                },
            });
    }

    updateAmount() {
        // Get selected payment IDs
        let ids = Object.entries(this.requiredPayments)
            .filter((entry: any) => entry[1] === true)
            .map((entry: any) => entry[0]);

        let contractPaymentList: any[] = [];
        let amountField =
            this.form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER'
                ? 'actualReceivedAmount'
                : 'finalAmount';

        // Get the current newPayment array
        contractPaymentList = [...this.newPayment];

        // Handle bounced payments
        if (this.form.controls['replacementOfBouncedPayment'].value) {
            const selectedBouncedPayments = this.bouncedPayments
                .filter((item: any) => ids.includes(item.id.toString()))
                .map((element: any) => {
                    return {
                        ...element,
                        amountOfPayment: element.amountOfPayment,
                        finalAmount: element.amountOfPayment,
                        actualReceivedAmount:
                            element.actualReceivedAmount || element.amountOfPayment,
                    };
                });

            contractPaymentList = contractPaymentList.concat(selectedBouncedPayments);
        }

        // Handle future payments
        if (
            !this.form.controls['replacementOfBouncedPayment'].value &&
            this.filteredFuturePayments.length
        ) {
            const selectedFuturePayments = this.filteredFuturePayments
                .filter((item: any) => ids.includes(item.id.toString()))
                .map((element: any) => {
                    return {
                        ...element,
                        amountOfPayment: element.amountOfPayment,
                        finalAmount: element.amountOfPayment,
                    };
                });

            contractPaymentList = contractPaymentList.concat(selectedFuturePayments);
        }
        if (!!contractPaymentList) {
            if (
                (contractPaymentList.length > 0 &&
                    contractPaymentList.some(
                        (item: any) => item.paymentType?.code === 'monthly_payment'
                    )) ||
                contractPaymentList.some(
                    (item: any) => item.typeOfPayment?.code === 'monthly_payment'
                )
            ) {
                this.form.get('required')?.setValue(true);
                this.disableRequired = true;
            } else {
                this.disableRequired = false;
            }
        }
        if (this.payingViaCreditCard) {
            this.form.get('required')?.setValue(false);
            this.disableRequired = true;
        }
        // Handle credit card payments
        if (this.form.controls['flagAsPayingViaCC'].value) {
            this.form.get('required')?.setValue(false);
            this.disableRequired = true;
            contractPaymentList = this.allPayments
                .filter((item: any) => ids.includes(item.id.toString()))
                .map((element: any) => {
                    return {
                        ...element,
                        amountOfPayment: element.amount,
                        finalAmount: element.amount,
                    };
                });
        }

        if (this.form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER') {
            const sumActualAmount = contractPaymentList.reduce(
                (sum: number, item: any) => {
                    const amount = item.actualReceivedAmount || 0;
                    return +sum + +amount;
                },
                0
            );

            const sumAmount = contractPaymentList.reduce((sum: number, item: any) => {
                const amount = item.finalAmount || 0;
                return +sum + +amount;
            }, 0);

            this.diff = Math.abs(sumAmount - sumActualAmount);
            this.signedDiff = sumActualAmount - sumAmount;
            this.showWarningMsg =
                this.diff > this.differenceThreshold && sumActualAmount;
            this.isLess = this.signedDiff <= this.differenceThreshold;

            this.form.controls['amount'].setValue(sumActualAmount);
        } else {
            const totalAmount = contractPaymentList.reduce(
                (sum: number, item: any) => {
                    const amount = item[amountField] || 0;
                    return +sum + +amount;
                },
                0
            );

            this.form.controls['amount'].setValue(totalAmount);
        }
    }

    calcAdd(value: any) {
        let result = 0;
        if (value?.includeWorkerSalary) {
            result = +value.amountOfPayment - +this.workerSalary;
        } else {
            result = +value.amountOfPayment;
        }
        if (+value.additionAmount > 0) {
            return Math.round((result / this.vat + +value.additionAmount) * this.vat);
        } else {
            return result;
        }
    }

    calcDis(value: any) {
        let result = 0;
        if (value?.includeWorkerSalary) {
            result = +value.amountOfPayment - +this.workerSalary;
        } else {
            result = +value.amountOfPayment;
        }
        if (+value.additionalDiscountAmount > 0) {
            return Math.floor(
                (result / this.vat - +value.additionalDiscountAmount) * this.vat
            );
        } else {
            return result;
        }
    }

    addSalary(val: number, includeWorkerSalary: boolean): number {
        let result = 0;
        if (includeWorkerSalary) {
            result = +val + +this.workerSalary;
        } else {
            result = +val;
        }
        return result;
    }

    getPayingViaCreditCardPayment() {
        this.addPaymentForApprovalsService
            .getPayingViaCreditCardPayment(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.allPayments = res;
                },
            });
    }

    saveOperation() {
        if (
            this.showWarningMessageForIPAM &&
            this.form.controls['flagAsPayingViaCC'].value
        ) {
            this.ccDialog.confirm(
                '',
                `Are you sure you want to flag this family as paying via CC? This family is an IPAM family, and the IPAM flow will send them a payment link ${this.popupWarningMessageDaysInIpamFlow} days before the paid end date.`,
                () => {
                    this.proceedWarningForIPAM();
                }
            );
            return;
        } else {
            this.saveMonthlyOperation();
        }
    }

    proceedWarningForIPAM() {
        this.saveMonthlyOperation();
    }

    saveMonthlyOperation() {
        // Get the current newPayment array
        const currentNewPayments = [...this.newPayment];

        // Check if any new payments are missing required fields
        if (
            currentNewPayments.some(
                (payment) =>
                    (payment.amountOfPayment === 0 &&
                        (!this.allowedWaivedPayments ||
                            payment.paymentType?.code !== 'monthly_payment')) ||
                    payment.paymentDate === '' ||
                    !payment.paymentType
            )
        ) {
            this.notifications.notifyError(
                "You can't add new payments without payment amount, date or type."
            );
            return;
        }
        // Check if any payment is marked as required for card payments
        if (
            currentNewPayments.some((payment) =>
                payment.paymentType?.tags?.some((tag: any) => {
                    return tag === 'required_in_add_payment_for_approval_page';
                })
            ) &&
            !this.form.get('required')?.value &&
            this.form.get('methodOfPaymentSelect')?.value === 'CARD' &&
            this.form.get('payingOnline')?.value
        ) {
            const requiredPayment = currentNewPayments.find((payment) =>
                payment.paymentType?.tags?.some((tag: any) => {
                    return tag === 'required_in_add_payment_for_approval_page';
                })
            );
            this.notifications.notifyError(
                `The '${requiredPayment?.paymentType?.text}' payment must be marked as required`
            );
            return;
        }
        // Form validation
        let isValid = true;

        // Basic validation
        if (!this.form.get('methodOfPaymentSelect')?.value) {
            isValid = false;
            this.notifications.notifyError('Method of payment is required');
            return;
        }

        // Additional validation for wire transfer
        if (this.form.get('methodOfPaymentSelect')?.value === 'WIRE_TRANSFER') {
            if (!this.form.get('paymentDate')?.value) {
                isValid = false;
                this.notifications.notifyError(
                    'Payment date is required for wire transfer'
                );
                return;
            }

            // Validate transfer reference if provided
            const transferRef = this.form.get('transferReference')?.value;
            if (transferRef && /[\W_]/g.test(transferRef)) {
                isValid = false;
                this.notifications.notifyError('Invalid transfer reference');
                return;
            }
        }

        // Validate receipt attachment if not paying online
        if (
            !this.form.get('payingOnline')?.value &&
            !this.form.get('receiptAttachment')?.value
        ) {
            isValid = false;
            this.notifications.notifyError('Receipt attachment is required');
            return;
        }

        if (isValid) {
            // Prepare data for submission
            const data: any = {
                contractPaymentTerm: {
                    id: +this.contractObj.activeContractPaymentTerm.id,
                },
                paymentMethod: this.form.get('methodOfPaymentSelect')?.value,
                amount: +this.form.get('amount')?.value || 0,
                description: this.form.get('description')?.value || '',
                payingOnline: this.form.get('payingOnline')?.value,
                payingViaCreditCard: this.form.get('flagAsPayingViaCC')?.value,
                transferReference: this.form.get('transferReference')?.value,
                replacementOfBouncedPayment: this.form.get(
                    'replacementOfBouncedPayment'
                )?.value,
                attachments: this.form.get('receiptAttachment')?.value
                    ? [{id: this.form.get('receiptAttachment')?.value[0].id}]
                    : [],
            };
            // Add expected date for wire transfer
            if (data.paymentMethod === 'WIRE_TRANSFER') {
                data.expectedDate = this.form.get('paymentDate')?.value + ' 00:00:00';
            }

            // Add replace of if applicable
            if (this.replaceOf) {
                data.replaceOf = {id: this.replaceOf};
            }
            if (data.paymentMethod === 'CARD' && data.payingOnline) {
                data.required = this.form.get('required')?.value;
            }
            // Prepare contract payment list
            let contractPaymentList: any[] = [];

            // Process new payments
            const newContractPaymentList = this.newPayment.map((payment: any) => {
                const paymentCopy = {...payment};

                const amount = paymentCopy.amountOfPayment;
                const finalAmount = paymentCopy.finalAmount;

                delete paymentCopy.amountOfPayment;

                if (paymentCopy.additionsDiscounts === 'additions') {
                    paymentCopy.additionalDiscountAmount = 0;
                }

                if (paymentCopy.additionsDiscounts === 'discounts') {
                    paymentCopy.additionAmount = 0;
                }

                delete paymentCopy.additionsDiscounts;
                delete paymentCopy.amendedAmount;
                delete paymentCopy.finalAmount;

                // Only include actualReceivedAmount for wire transfers
                if (this.form.get('methodOfPaymentSelect')?.value !== 'WIRE_TRANSFER') {
                    delete paymentCopy.actualReceivedAmount;
                }

                if (!paymentCopy.affectedByCreditNote) {
                    paymentCopy.moreAdditionalDiscount = 0;
                }

                if (paymentCopy.prorated) {
                    paymentCopy.affectedByCreditNote = false;
                    paymentCopy.moreAdditionalDiscount = 0;
                    paymentCopy.additionalDiscountAmount = 0;
                    paymentCopy.additionAmount = 0;
                }

                delete paymentCopy.affectedByCreditNote;

                return {
                    ...paymentCopy,
                    additionAmount: paymentCopy.additionAmount
                        ? paymentCopy.additionAmount
                        : 0,
                    additionalDiscountAmount:
                        paymentCopy.additionalDiscountAmount === null
                            ? 0
                            : paymentCopy.additionalDiscountAmount,
                    moreAdditionalDiscount:
                        paymentCopy.moreAdditionalDiscount === null
                            ? 0
                            : paymentCopy.moreAdditionalDiscount,
                    paymentDate: paymentCopy.paymentDate + ' 00:00:00',
                    paymentType: {id: paymentCopy.paymentType.id},
                    subType: paymentCopy.subType ? {id: paymentCopy.subType} : null,
                    amount: finalAmount >= 0 ? finalAmount : amount,
                };
            });

            // Get selected payment IDs
            const ids = Object.entries(this.requiredPayments)
                .filter((entry: any) => entry[1])
                .map((entry: any) => +entry[0]);

            // Process bounced payments if applicable
            if (
                this.form.get('replacementOfBouncedPayment')?.value &&
                !this.form.get('flagAsPayingViaCC')?.value
            ) {
                contractPaymentList = this.bouncedPayments
                    .filter((payment) => ids.includes(+payment.id))
                    .map((payment) => {
                        const paymentData: any = {
                            replacedBouncedPaymentId: payment.id,
                            paymentDate: payment.dateOfPayment + ' 00:00:00',
                            amount: payment.amountOfPayment,
                            discountAmount: this.form.get('discountAmount')?.value || 0,
                        };

                        // Only include actualReceivedAmount for wire transfers
                        if (this.form.get('methodOfPaymentSelect')?.value === 'WIRE_TRANSFER') {
                            paymentData.actualReceivedAmount = payment.actualReceivedAmount;
                        }

                        return paymentData;
                    });
            } else if (
                !this.form.get('replacementOfBouncedPayment')?.value &&
                this.filteredFuturePayments.length &&
                !this.form.get('flagAsPayingViaCC')?.value
            ) {
                // Process future payments if applicable
                contractPaymentList = this.filteredFuturePayments
                    .filter((payment) => ids.includes(+payment.id))
                    .map((payment) => {
                        const paymentData: any = {
                            replacedFuturePaymentId: payment.id,
                            paymentDate: payment.dateOfPayment + ' 00:00:00',
                            amount: payment.amountOfPayment,
                            paymentType: {id: payment.typeOfPayment.id},
                        };

                        // Only include actualReceivedAmount for wire transfers
                        if (this.form.get('methodOfPaymentSelect')?.value === 'WIRE_TRANSFER') {
                            paymentData.actualReceivedAmount = payment.actualReceivedAmount;
                        }

                        return paymentData;
                    });
            }

            // Process credit card payments if applicable
            if (this.form.get('flagAsPayingViaCC')?.value) {
                contractPaymentList = this.allPayments
                    .filter((payment) => ids.includes(+payment.id))
                    .map((payment) => {
                        return {
                            paymentDate: payment.dateOfPayment + ' 00:00:00',
                            amount: payment.amount,
                            paymentType: {id: payment.paymentType.id},
                            status: payment.status.value,
                        };
                    });
            }

            // Combine all payment lists
            contractPaymentList = contractPaymentList.concat(newContractPaymentList);

            // Validate wire transfer payments
            if (
                contractPaymentList.some((payment) => {
                    return !payment.actualReceivedAmount;
                }) &&
                this.form.get('methodOfPaymentSelect')?.value === 'WIRE_TRANSFER'
            ) {
                this.notifications.notifyError(
                    "You can't add payments without actual received amount."
                );
                return;
            }

            // Validate that at least one payment is selected
            if (contractPaymentList.length === 0) {
                this.notifications.notifyError(
                    'You Should Select Or Add Payments Before Saving.'
                );
                return;
            }

            // Add contract payment list to data
            data.contractPaymentList = contractPaymentList;

            // Add related entity info if available
            if (this.clientEnchanterTodoId) {
                data.relatedEntityId = this.clientEnchanterTodoId;
                data.relatedEntityType = 'ClientEnchanterTodo';
            }

            // Process based on contract prospect type
            if (this.contractProspectType !== 'maidvisa.ae_prospect') {
                if (!this.form.get('flagAsPayingViaCC')?.value) {
                    // Check if user forgot to mark payment as prorated
                    this.addPaymentForApprovalsService
                        .checkIfUserForgetToMarkPaymentAsProrated(
                            this.currentContractId,
                            data
                        )
                        .subscribe({
                            next: (userForget) => {
                                if (userForget) {
                                    this.ccDialog.confirm(
                                        '',
                                        'You may have added a prorated payment for this family but it seems that you didn\'t check the "is prorated" checkbox. Are you sure that you want to proceed?',
                                        () => {
                                            this.doSaveOperation(data);
                                        },
                                        () => {
                                        },
                                        'Proceed anyway',
                                        'Cancel'
                                    );
                                } else {
                                    this.doSaveOperation(data);
                                }
                            },
                        });
                } else {
                    this.checkNextMonthPayment(data);
                }
            } else {
                // Check for monthly payments without required flags
                let isMonthlyValid = true;
                this.newPayment.forEach((payment: any) => {
                    if (
                        payment.paymentType &&
                        payment.paymentType.code === 'monthly_payment' &&
                        !(payment.includeWorkerSalary || payment.initial)
                    ) {
                        isMonthlyValid = false;
                    }
                });

                if (isMonthlyValid) {
                    this.checkNextMonthPayment(data);
                } else {
                    this.ccDialog.confirm(
                        '',
                        `You added a monthly payment for this contract without selecting if it "is initial" or if it "includes worker salary".Please check one of these 2 checkboxes to proceed.`,
                        () => {
                        },
                        () => {
                        },
                        'Ok'
                    );
                }
            }
        }
    }

    checkNextMonthPayment(data: any) {
        if (data.payingViaCreditCard) {
            let nextMonthPayment = null;
            data.contractPaymentList.forEach((payment: any) => {
                const paymentYear = +payment.paymentDate.split(' ')[0].split('-')[0];
                const paymentMonth = +payment.paymentDate.split(' ')[0].split('-')[1];
                const currentMonth = +(new Date().getMonth() + 1);
                const currentYear = +new Date().getFullYear();
                if (paymentYear === currentYear && paymentMonth === currentMonth + 1) {
                    nextMonthPayment = payment;
                }
            });
            if (nextMonthPayment === null) {
                this.ccDialog.confirm(
                    '',
                    'You have not chosen the first next monthly payment to be collected. Please consider doing so.',
                    () => {
                        this.doSaveOperation(data);
                    },
                    () => {
                    },
                    'Proceed anyway',
                    'Cancel'
                );
            } else {
                this.doSaveOperation(data);
            }
        } else {
            this.doSaveOperation(data);
        }
    }

    doSaveOperation(data: any) {
        data = {...data, clientTodoInfo: this.clientTodoInfo};
        if (data.clientTodoInfo == undefined) {
            data.clientTodoInfo = null;
        }
        console.log(data);

        this.addPaymentForApprovalsService
            .validateCreateToDoFromErp(data)
            .subscribe({
                next: (res: any) => {
                    if (res.valid) {
                        this.checkForExistingTokenizedPayments(data);
                    } else {
                        this.dataToConfirm = data;
                        if (data.payingViaCreditCard) {
                            this.duplicatedPayment.todoId = res.todoId;
                            this.duplicatedPayment.todoUuid = res.Uuid;
                            this.checkMatchedToDoIfRelatedToRunningFlow();
                        } else {
                            this.openDuplicatedDialog(res);
                        }
                    }
                },
            });
    }

    checkForExistingTokenizedPayments(data: any) {
        this.addPaymentForApprovalsService
            .checkForExistingTokenizedPayments(data)
            .subscribe({
                next: (res: any) => {
                    if (res.hasMatches) {
                        let matchesDates = res.matches
                            .map((item: any) => item.date)
                            .join(', ');
                        this.ccDialog.confirm(
                            '',
                            `You're adding a monthly payment for ${matchesDates} that already has a scheduled tokenized monthly payment. Creating this payment will automatically delete the existing tokenized payment scheduled for deduction at the beginning of next month`,
                            () => {
                                this.confirmCreate(data);
                            },
                            () => {
                            },
                            'Proceed anyway',
                            'Cancel'
                        );
                    }
                    this.confirmCreate(data);
                },
            });
    }

    checkMatchedToDoIfRelatedToRunningFlow() {
        this.addPaymentForApprovalsService
            .checkMatchedToDoIfRelatedToRunningFlow(this.duplicatedPayment.todoId)
            .subscribe({
                next: (res: any) => {
                    if (!this.form.controls['flagAsPayingViaCC'].value) {
                        if (!res.hasActiveRunningFlow) {
                            this.confirmCreate(this.dataToConfirm);
                        } else {
                            this.dataMatched = {
                                flowId: res.flowId,
                                flowName: res.flowName
                                    .split('_')
                                    .map(
                                        (word: string) =>
                                            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                                    )
                                    .join(' '),
                                required: res.required,
                            };
                            this.ccDialog.confirm(
                                'Duplicated payment',
                                `The added payment is duplicated and already has a running collection flow "${this.dataMatched.flowName}", are you sure that you want to stop the current flow and run the reminder flow?`,
                                () => {
                                    this.proceedStopRunningFlow();
                                },
                                () => {
                                },
                                'Proceed',
                                'Close'
                            );
                        }
                    } else {
                        this.dataMatched = {
                            flowId: res.flowId,
                            flowName: res.flowName,
                            required: res.required,
                        };
                        this.dataToConfirm.required = res.required;
                        this.ccDialog.confirm(
                            'Duplicated running flow',
                            `The contract has "${this.dataMatched.flowName}" flow running, are you sure that you want to stop it and start Paying via CC flow?`,
                            () => {
                                this.proceedStopRunningFlow();
                            },
                            () => {
                            },
                            this.duplicatedPayment.todoUuid ? 'Yes' : '',
                            'Close'
                        );
                    }
                },
            });
    }

    proceedStopRunningFlow() {
        this.addPaymentForApprovalsService
            .proceedStopRelatedFlowAndCreateToDo(
                this.duplicatedPayment.todoId,
                this.dataMatched.flowId,
                this.dataToConfirm
            )
            .subscribe({
                next: () => {
                    this.notifications.notifySuccess('Payment inserted successfully');
                    this.cancelOperation();
                },
            });
    }

    cancelOperation() {
        this.router.navigateByUrl(
            `/client/payments/${this.currentClientID}/${this.currentContractId}`
        );
    }

    checkShowDiscount() {
        let oneSelected = false;
        if (
            this.form.controls['replacementOfBouncedPayment'].value &&
            this.bouncedPayments?.length > 0
        ) {
            Object.keys(this.requiredPayments).forEach((key) => {
                if (this.requiredPayments[key]) {
                    oneSelected = true;
                }
            });
        }
        return oneSelected;
    }

    // This method is intentionally left empty as it's implemented below
    checkIfUserForgetToMarkPaymentAsProrated(data: any): Observable<any> {
        return this.addPaymentForApprovalsService.checkIfUserForgetToMarkPaymentAsProrated(
            this.currentContractId,
            data
        );
    }

    addNewPayment() {
        const newPayment = {
            replacedBouncedPaymentId: null,
            replacedFuturePaymentId: null,
            paymentDate: '',
            amountOfPayment: 0,
            prorated: false,
            affectedByAdditionalDiscount: false,
            initial: false,
            includeWorkerSalary: false,
            disableIncludeWorkerSalary: false,
            actualReceivedAmount: 0,
            paymentType: null,
            subType: null,
            rowNumber: this.rowCount++,
            disableAmount: true,
            affectedByCreditNote: false,
            additionsDiscounts: '',
            additionAmount: 0,
            additionalDiscountAmount: 0,
            moreAdditionalDiscount: 0,
            finalAmount: '',
            disableAdditionsDiscounts: false,
            disableAdditionAmount: false,
            disableMoreAdditionalDiscount: false,
            isVATTED: '',
        };

        this.newPayment.push(newPayment);

        // Create a proxy to track changes to the payment object
        this.setupPaymentWatcher(this.newPayment.length - 1);
    }

    /**
     * Sets up a watcher for a payment object in the newPayment array
     * This is equivalent to $scope.$watch('newPayments', ...) in AngularJS
     */
    setupPaymentWatcher(index: number): void {
        const payment = this.newPayment[index];
        const oldValues: any = {};

        // Store initial values
        Object.keys(payment).forEach((key) => {
            oldValues[key] = payment[key];
        });

        // Set up interval to check for changes
        const intervalId = setInterval(() => {
            Object.keys(payment).forEach((key) => {
                if (payment[key] !== oldValues[key]) {
                    // Call the change handler with the property that changed
                    this.handleNewPaymentChanges(
                        index,
                        key,
                        payment[key],
                        oldValues[key]
                    );
                    // Update the old value
                    oldValues[key] = payment[key];
                }
            });
        }, 200); // Check every 200ms

        // Store the interval ID to clear it when the component is destroyed
        this.paymentWatcherIntervals.push(intervalId);
    }

    deleteNewPayment(newPayment: any) {
        if (newPayment.prorated) {
            this.proratedCount--;
        } else {
            this.initialCount--;
        }

        // Find the index of the payment to delete
        const index = this.newPayment.findIndex(
            (item: any) => item.rowNumber === newPayment.rowNumber
        );

        // Clear the interval for this payment if it exists
        if (index !== -1 && this.paymentWatcherIntervals[index]) {
            clearInterval(this.paymentWatcherIntervals[index]);
            this.paymentWatcherIntervals.splice(index, 1);
        }

        this.newPayment = this.newPayment.filter((item: any) => {
            return item.rowNumber !== newPayment.rowNumber;
        });
    }

    ngOnDestroy(): void {
        // Restore the display of breadcrumbs-container to show normal breadcrumb behavior
        if (this.breadcrumbsComponent) {
            const breadcrumbsContainer = this.breadcrumbsComponent.nativeElement.querySelector('.breadcrumbs-container');
            if (breadcrumbsContainer) {
                breadcrumbsContainer.style.display = 'block';
            }
        }

        // Clear all the payment watcher intervals
        this.paymentWatcherIntervals.forEach((intervalId) => {
            clearInterval(intervalId);
        });

        this.destroy$.next();
        this.destroy$.complete();
    }

    /**
     * Handles checkbox changes for payment selection
     * This method is called from the template when checkboxes are changed
     */
    onPaymentSelectionChange(): void {
        this.updateAmount();
    }

    updateFinalAmount(payment: any) {
        let finalAmount = payment.amountOfPayment || 0;

        // Add additions if applicable
        if (payment.additionsDiscounts === 'additions' && payment.additionAmount) {
            finalAmount += +payment.additionAmount;
        }

        // Subtract discounts if applicable
        if (
            payment.additionsDiscounts === 'discounts' &&
            payment.additionalDiscountAmount
        ) {
            finalAmount -= +payment.additionalDiscountAmount;
        }

        // Apply credit note discount if applicable
        if (payment.affectedByCreditNote && payment.moreAdditionalDiscount) {
            finalAmount -= +payment.moreAdditionalDiscount;
        }

        payment.finalAmount = finalAmount;

        // Update the total amount
        this.updateAmount();
    }

    disableProrated(payment: any) {
        if (payment.paymentType && payment.paymentType.code == 'monthly_payment') {
            return (
                this.proratedCount >= this.allowedProRatedPayments && !payment.prorated
            );
        } else {
            return true;
        }
    }

    changeInitial(payment: any) {
        if (payment.initial) {
            this.initialCount++;
        } else {
            this.initialCount--;
        }
        setTimeout(() => {
            if (payment.initial) {
                payment.includeWorkerSalary = false;
                payment.disableIncludeWorkerSalary = true;
            } else {
                payment.disableIncludeWorkerSalary = false;
            }
        });
    }

    changeProrated(payment: any) {
        if (payment.prorated) {
            this.proratedCount++;
        } else {
            this.proratedCount--;
        }
    }

    disableInitial(payment: any) {
        if (payment.paymentType && payment.paymentType.code === 'monthly_payment') {
            return (
                this.initialCount >= this.allowedInitialPayments && !payment.initial
            );
        } else {
            return true;
        }
    }

    copy(text: string = '') {
        if (text) {
            navigator.clipboard.writeText(text);
            this.notifications.notifySuccess('Copied to clipboard successfully');
        } else {
            this.notifications.notifyError('Not Copied to clipboard');
        }
    }

    setReactivationPaymentForTodo() {
        this.addPaymentForApprovalsService
            .setReactivationPaymentForTodo(this.duplicatedPayment.todoUuid)
            .subscribe();
    }

    getFuturePayments() {
        this.addPaymentForApprovalsService
            .getFuturePayments(this.currentContractId)
            .subscribe({
                next: (res: any) => {
                    this.futurePayments = res;
                    this.filteredFuturePayments = res;
                    this.tempFuturePayments = [...this.futurePayments];
                    this.updateAmount();
                },
            });
    }

    getPaymentDiscountInfo(index: number, paymentTypeId: any) {
        let params: any = {};
        if (this.newPayment[index].paymentDate) {
            params = {
                date: this.newPayment[index].paymentDate,
            };
        }
        if (this.newPayment[index].prorated) {
            params = {
                ...params,
                isProRated: true,
            };
        }
        if (this.newPayment.some((e) => e.prorated)) {
            params = {
                ...params,
                proRatedAdded: true,
            };
        }

        this.addPaymentForApprovalsService
            .getPaymentDiscountInfo(this.currentContractId, paymentTypeId, params)
            .subscribe({
                next: (response: any) => {
                    this.newPayment[index].affectedByCreditNote = false;
                    this.newPayment[index].isVATTED =
                        response.isVATTED != undefined ? response.isVATTED : false;
                    this.newPayment[index].amountOfPayment = response.amount;
                    this.newPayment[index].additionAmount =
                        response.additionAmount != undefined ? response.additionAmount : 0;
                    this.newPayment[index].additionalDiscountAmount =
                        response.additionalDiscount != undefined
                            ? response.additionalDiscount
                            : 0;
                    this.newPayment[index].moreAdditionalDiscount =
                        response.moreAdditionalDiscount != undefined
                            ? response.moreAdditionalDiscount
                            : 0;
                    this.vat = response.isVATTED ? 1 + response.vatPercent : 1;
                    this.newPayment[index].additionsDiscounts =
                        response.additionalDiscount != undefined &&
                        response.additionalDiscount > 0
                            ? 'discounts'
                            : '';
                    this.newPayment[index].finalAmount = 0;

                    if (this.newPayment[index].prorated) {
                        this.newPayment[index].affectedByCreditNote = false;
                        this.newPayment[index].disableAdditionsDiscounts = true;
                        this.newPayment[index].disableAmount = false;
                        this.newPayment[index].additionsDiscounts = '';
                        this.newPayment[index].finalAmount =
                            this.newPayment[index].amountOfPayment;
                    } else {
                        if (response.moreAdditionalDiscount > 0) {
                            this.newPayment[index].disableMoreAdditionalDiscount = false;
                            this.newPayment[index].affectedByCreditNote = true;
                            this.newPayment[index].finalAmount = Math.round(
                                (this.newPayment[index].amountOfPayment / this.vat -
                                    this.newPayment[index].moreAdditionalDiscount) *
                                this.vat
                            );
                        } else {
                            this.newPayment[index].disableMoreAdditionalDiscount = true;
                        }
                        if (response.amount > 0) {
                            this.newPayment[index].disableAmount = true;
                            this.newPayment[index].disableAdditionAmount = false;
                        } else {
                            this.newPayment[index].disableAmount = false;
                            this.newPayment[index].disableAdditionAmount = true;
                        }
                        this.newPayment[index].includeWorkerSalary = false;
                        this.newPayment[index].prorated = false;
                        if (response.includeWorkerSalary) {
                            this.newPayment[index].disableIncludeWorkerSalary = true;
                            this.newPayment[index].includeWorkerSalary = true;
                        } else {
                            this.newPayment[index].disableIncludeWorkerSalary = false;
                        }
                        if (this.newPayment[index].additionsDiscounts) {
                            if (this.newPayment[index].additionsDiscounts == 'discounts') {
                                this.newPayment[index].additionAmount = '';
                                this.newPayment[index].additionalDiscountAmount =
                                    response.additionalDiscount != undefined
                                        ? response.additionalDiscount
                                        : 0;
                                if (!isNaN(this.newPayment[index].additionalDiscountAmount)) {
                                    this.newPayment[index].amendedAmount = Math.floor(
                                        (this.newPayment[index].amountOfPayment / this.vat -
                                            this.newPayment[index].additionalDiscountAmount) *
                                        this.vat
                                    );
                                    if (this.newPayment[index].affectedByCreditNote) {
                                        this.newPayment[index].finalAmount = Math.round(
                                            (this.newPayment[index].amendedAmount / this.vat -
                                                this.newPayment[index].moreAdditionalDiscount) *
                                            this.vat
                                        );
                                    } else {
                                        this.newPayment[index].finalAmount =
                                            this.newPayment[index].amendedAmount;
                                    }
                                }
                            }
                        }
                    }
                    this.updateAmount();
                },
            });
    }
}
