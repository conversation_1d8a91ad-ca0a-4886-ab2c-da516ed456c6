<cc-breadcrumbs
        [suppressNavigation]="true"
        [links]="breadcrumbs1"
></cc-breadcrumbs>
<div class="acc-7821 p-4">
  <h3
    class="text-center text-danger py-2 border-top border-bottom border-2"
    style="font-weight: 500; margin-top: 0"
    *ngIf="client"
  >
    {{ client?.name ? client.name : client?.mobileNumber }}
  </h3>
  <form [formGroup]="form" class="form-horizontal">
    <div *ngIf="!contractObj?.clientPaidVat" class="alert alert-danger mb-3">
      Contract is not Vatted!
    </div>

    <div class="container-fluid">
      <div class="row mb-4">
        <!-- Left Column -->
        <div class="col-sm-6 mt-3">
          <div class="mb-3">
            <cc-input
              label="Amount"
              formControlName="amount"
              [required]="true"
              [disabled]="true"
            ></cc-input>
          </div>

          <div class="mb-3" *ngIf="checkShowDiscount()">
            <cc-input
              label="Discount"
              formControlName="discountAmount"
              [disabled]="disableDiscount"
            ></cc-input>
          </div>

          <div class="mb-3" *ngIf="!hideReplaceBounced">
            <cc-checkbox
              formControlName="replacementOfBouncedPayment"
              [disabled]="bouncedPaymentId !== ''"
              >Replacement of a bounced payment</cc-checkbox
            >
          </div>
        </div>

        <!-- Right Column -->
        <div class="col-sm-6 mt-3">
          <div class="mb-3">
            <cc-select
              label="Method of Payment"
              [data]="methodOfPaymentOptions"
              [required]="true"
              formControlName="methodOfPaymentSelect"
            ></cc-select>
          </div>

          <div
            class="mb-3"
            *ngIf="form.controls['methodOfPaymentSelect'].value == 'CARD'"
          >
            <cc-checkbox
              formControlName="payingOnline"
              [disabled]="form.controls['flagAsPayingViaCC'].value"
              >Paying Online</cc-checkbox
            >
          </div>
          <div
            class="mb-3"
            *ngIf="
              form.controls['methodOfPaymentSelect'].value == 'CARD' &&
              form.controls['payingOnline'].value
            "
          >
            <cc-checkbox formControlName="required" [disabled]="disableRequired"
              >Required</cc-checkbox
            >
          </div>

          <div
            class="mb-3"
            *ngIf="form.controls['methodOfPaymentSelect'].value == 'CARD'"
          >
            <cc-checkbox
              [disabled]="payingViaCreditCard"
              formControlName="flagAsPayingViaCC"
              >Flag as paying via CC</cc-checkbox
            >
            <div *ngIf="payingViaCreditCard" class="text-danger mt-1">
              Contract already flagged as Paying via CC
            </div>
          </div>

          <div
            class="mb-3"
            *ngIf="
              form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER'
            "
          >
            <cc-input
              label="Transfer Reference"
              formControlName="transferReference"
            ></cc-input>
          </div>

          <div
            class="mb-3"
            *ngIf="
              form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER'
            "
          >
            <cc-datepicker
              label="Expected Date"
              formControlName="paymentDate"
              [required]="true"
            ></cc-datepicker>
          </div>
        </div>
      </div>

      <!-- Bottom Row -->
      <div class="row mb-4">
        <div class="col-12" *ngIf="!form.controls['payingOnline'].value">
          <div class="mb-3">
            <cc-file-uploader
              class="col-md-12"
              [label]="
                form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER'
                  ? 'Proof of Transfer'
                  : 'Receipt'
              "
              formControlName="receiptAttachment"
              tag="paymentAttachement"
              [required]="!form.controls['payingOnline'].value"
              [dropzoneConfig]="config"
            ></cc-file-uploader>
          </div>
        </div>
        <div class="col-12">
          <div class="mb-3 col-md-12">
            <cc-textarea
              label="Description"
              formControlName="description"
              rows="3"
              style="width: 100%"
            ></cc-textarea>
          </div>
        </div>
      </div>
    </div>
    <div
      class="row"
      *ngIf="
        form.controls['replacementOfBouncedPayment'].value &&
        !form.controls['flagAsPayingViaCC'].value
      "
    >
      <div class="col-12">
        <h4 class="py-2 border-top border-2"><b>Bounced Payments</b></h4>
      </div>
      <div class="col-12">
        <div
          class="table-responsive"
          style="max-height: 600px; overflow-y: auto"
        >
          <table class="table table-striped small">
            <thead class="cc-secondary-background">
              <tr>
                <th class="text-white text-center" style="width: 15%">
                  Select
                </th>
                <th class="text-white text-center" style="width: 25%">
                  Payment Date
                </th>
                <th class="text-white text-center" style="width: 25%">
                  Payment Amount
                </th>
                <th
                  class="text-white text-center"
                  style="width: 35%"
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ===
                    'WIRE_TRANSFER'
                  "
                >
                  Actual Received Amount
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let payment of bouncedPayments">
                <td class="text-center align-middle">
                  <cc-checkbox
                    [(ngModel)]="requiredPayments[payment.id]"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onPaymentSelectionChange()"
                  ></cc-checkbox>
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.dateOfPayment }}
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.amountOfPayment }}
                </td>
                <td
                  class="text-center align-middle"
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ===
                    'WIRE_TRANSFER'
                  "
                >
                  <cc-amount-input
                    [(ngModel)]="payment.actualReceivedAmount"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="updateAmount()"
                    symbol=""
                  ></cc-amount-input>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div
      class="row"
      [hidden]="
        form.controls['flagAsPayingViaCC'].value ||
        form.controls['replacementOfBouncedPayment'].value
      "
    >
      <div class="col-12">
        <h4 class="py-2 border-top border-2"><b>Existing Payments</b></h4>
      </div>
      <div class="col-12">
        <div
          class="table-responsive"
          style="max-height: 600px; overflow-y: auto"
        >
          <table class="table table-striped small">
            <thead class="cc-secondary-background">
              <tr>
                <th class="text-white text-center" style="width: 10%">
                  Select
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Payment Date
                </th>
                <th class="text-white text-center" style="width: 20%">
                  <cc-select
                    style="height: 3rem"
                    [(ngModel)]="paymentType"
                    label="Type Of Payment"
                    [ngModelOptions]="{ standalone: true }"
                    [lazyPageFetcher]="typeOfPaymentOptions"
                    (userSelect)="onPaymentTypeChange($event)"
                    [emitFullSelectOption]="true"
                  ></cc-select>
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Sub Type
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Payment Amount
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Payment Status
                </th>
                <th
                  class="text-white text-center"
                  style="width: 20%"
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ===
                    'WIRE_TRANSFER'
                  "
                >
                  Actual Received Amount
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let payment of filteredFuturePayments">
                <td class="text-center align-middle">
                  <cc-checkbox
                    [(ngModel)]="requiredPayments[payment.id]"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onPaymentSelectionChange()"
                  ></cc-checkbox>
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.dateOfPayment }}
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.typeOfPayment?.label }}
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.subType?.label }}
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.amountOfPayment }}
                </td>
                <td class="text-center align-middle text-center">
                  {{ payment.status?.label }}
                </td>
                <td
                  class="text-center align-middle text-center"
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ===
                    'WIRE_TRANSFER'
                  "
                >
                  <cc-input
                    type="number"
                    [(ngModel)]="payment.actualReceivedAmount"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="updateAmount()"
                  ></cc-input>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="row" [hidden]="form.controls['flagAsPayingViaCC'].value">
      <div
        class="col-12 border-top border-2 d-flex justify-content-between pt-2"
      >
        <div class="col-auto px-0">
          <h4 class="py-2"><b>New Payments</b></h4>
        </div>
        <div class="col-auto px-0">
          <button cc-raised-button (click)="addNewPayment()">
            Add Payment
          </button>
        </div>
      </div>
      <div class="col-12">
        <div
          class="table-responsive"
          style="max-height: 600px; overflow-y: auto"
        >
          <table class="table table-striped small">
            <colgroup>
              <col span="1" style="width: 5%" />
              <col span="1" style="width: 10%" />
              <col span="1" style="width: 10%" />
              <col span="1" style="width: 10%" />
              <col span="1" style="width: 10%" />
              <col span="1" style="width: 10%" />
              <col span="1" style="width: 25%" />
              <col span="1" style="width: 25%" />
              <col
                span="1"
                *ngIf="contractProspectType != 'maidvisa.ae_prospect'"
              />
            </colgroup>
            <thead class="cc-secondary-background">
              <tr>
                <th class="text-white text-center" style="width: 5%">Cancel</th>
                <th class="text-white text-center" style="width: 10%">
                  Payment Date
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Payment Type
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Sub Type
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Payment Amount
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Additions and Discounts
                </th>
                <th class="text-white text-center" style="width: 15%">
                  Credit Note Discount
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Final Amount
                </th>
                <th
                  class="text-white text-center"
                  style="width: 8%"
                  *ngIf="contractProspectType != 'maidvisa.ae_prospect'"
                >
                  Is Prorated
                </th>
                <th
                  class="text-white text-center"
                  style="width: 12%"
                  *ngIf="contractProspectType == 'maidvisa.ae_prospect'"
                >
                  Include Worker Salary
                </th>
                <th
                  class="text-white text-center"
                  style="width: 8%"
                  *ngIf="contractProspectType == 'maidvisa.ae_prospect'"
                >
                  Is Initial
                </th>
                <th
                  class="text-white text-center"
                  style="width: 12%"
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ==
                    'WIRE_TRANSFER'
                  "
                >
                  Actual Received Amount
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let payment of newPayment">
                <td class="text-center align-middle text-center">
                  <button
                    cc-icon-button
                    color="warn"
                    (click)="deleteNewPayment(payment)"
                  >
                    <cc-icon>delete</cc-icon>
                  </button>
                </td>
                <td
                  class="text-center align-middle text-center"
                  style="max-width: 200px"
                >
                  <cc-datepicker
                    [(ngModel)]="payment.paymentDate"
                    [ngModelOptions]="{ standalone: true }"
                  ></cc-datepicker>
                </td>
                <td
                  class="text-center align-middle text-center"
                  style="max-width: 200px"
                >
                  <cc-select
                    [(ngModel)]="payment.paymentType"
                    [ngModelOptions]="{ standalone: true }"
                    [lazyPageFetcher]="typeOfPaymentOptions"
                    [emitFullSelectOption]="true"
                  ></cc-select>
                </td>
                <td
                  class="text-center align-middle text-center"
                  style="max-width: 200px"
                >
                  <cc-select
                    [(ngModel)]="payment.subType"
                    [ngModelOptions]="{ standalone: true }"
                    [lazyPageFetcher]="subTypeOfPaymentOptions"
                  ></cc-select>
                </td>
                <td
                  class="text-center align-middle text-center"
                  style="max-width: 200px"
                >
                  <cc-input
                    type="number"
                    [(ngModel)]="payment.amountOfPayment"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="payment.disableAmount"
                    (ngModelChange)="updateFinalAmount(payment)"
                  ></cc-input>
                </td>
                <td class="text-center align-middle">
                  <div class="row pl-2">
                    <div class="col-md-6" style="padding: 0">
                      <cc-radio-group
                        class="d-flex flex-column align-items-start"
                        [(ngModel)]="payment.additionsDiscounts"
                        [ngModelOptions]="{ standalone: true }"
                        (ngModelChange)="updateFinalAmount(payment)"
                      >
                        <cc-radio-button
                          [disabled]="
                            payment.disableAdditionAmount ||
                            payment.disableAdditionsDiscounts ||
                            payment.paymentType?.code == 'overstay_fee'
                          "
                          value="additions"
                          >Additions</cc-radio-button
                        >
                        <cc-radio-button
                          [disabled]="payment.disableAdditionsDiscounts"
                          value="discounts"
                          >Discounts</cc-radio-button
                        >
                      </cc-radio-group>
                    </div>
                    <div class="col-md-6" style="padding: 0">
                      <cc-input
                        class="mx-1"
                        type="number"
                        *ngIf="payment.additionsDiscounts == 'additions'"
                        [(ngModel)]="payment.additionAmount"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="
                          payment.disableAdditionAmount ||
                          payment.disableAdditionsDiscounts ||
                          payment.paymentType?.code == 'overstay_fee'
                        "
                        (ngModelChange)="updateFinalAmount(payment)"
                      ></cc-input>
                      <cc-input
                        class="mx-1"
                        type="number"
                        *ngIf="payment.additionsDiscounts == 'discounts'"
                        [(ngModel)]="payment.additionalDiscountAmount"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="payment.disableAdditionsDiscounts"
                        (ngModelChange)="updateFinalAmount(payment)"
                      ></cc-input>
                    </div>
                  </div>
                </td>
                <td class="text-center align-middle">
                  <cc-checkbox
                    [(ngModel)]="payment.affectedByCreditNote"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="
                      payment.disableAdditionsDiscounts ||
                      payment.disableMoreAdditionalDiscount ||
                      payment.paymentType?.code == 'overstay_fee'
                    "
                    (ngModelChange)="updateFinalAmount(payment)"
                    >{{ payment.moreAdditionalDiscount }}</cc-checkbox
                  >
                </td>
                <td class="text-center align-middle" style="max-width: 150px">
                  <cc-input
                    type="number"
                    [(ngModel)]="payment.finalAmount"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="true"
                  ></cc-input>
                </td>
                <td
                  class="text-center align-middle"
                  *ngIf="contractProspectType != 'maidvisa.ae_prospect'"
                >
                  <cc-checkbox
                    [(ngModel)]="payment.prorated"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="disableProrated(payment)"
                    (ngModelChange)="changeProrated(payment)"
                  ></cc-checkbox>
                </td>
                <td
                  class="text-center align-middle"
                  *ngIf="contractProspectType == 'maidvisa.ae_prospect'"
                >
                  <cc-checkbox
                    [(ngModel)]="payment.includeWorkerSalary"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="
                      payment.disableIncludeWorkerSalary ||
                      payment.paymentType?.code != 'monthly_payment'
                    "
                  ></cc-checkbox>
                </td>
                <td
                  class="text-center align-middle"
                  *ngIf="contractProspectType == 'maidvisa.ae_prospect'"
                >
                  <cc-checkbox
                    [(ngModel)]="payment.initial"
                    [ngModelOptions]="{ standalone: true }"
                    [disabled]="disableInitial(payment)"
                    (ngModelChange)="changeInitial(payment)"
                  ></cc-checkbox>
                </td>
                <td
                  *ngIf="
                    form.controls['methodOfPaymentSelect'].value ===
                    'WIRE_TRANSFER'
                  "
                  width="10%"
                >
                  <cc-input
                    type="number"
                    [(ngModel)]="payment.actualReceivedAmount"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onPaymentSelectionChange()"
                  ></cc-input>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="row" *ngIf="form.controls['flagAsPayingViaCC'].value">
      <div class="col-12 border-top border-2">
        <h4><b>All Payments</b></h4>
      </div>
      <div class="col-12">
        <div
          class="table-responsive"
          style="max-height: 600px; overflow-y: auto"
        >
          <table class="table table-striped small">
            <thead class="cc-secondary-background">
              <tr>
                <th class="text-white text-center" style="width: 10%">
                  Select
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Payment Date
                </th>
                <th class="text-white text-center" style="width: 10%">
                  Payment Amount
                </th>
                <th class="text-white text-center" style="width: 25%">
                  Payment Status
                </th>
                <th class="text-white text-center" style="width: 25%">
                  <cc-select
                    label="Type Of Payment"
                    [(ngModel)]="allTypeFilter"
                    [lazyPageFetcher]="typeOfPaymentOptions"
                    [emitFullSelectOption]="true"
                    [ngModelOptions]="{ standalone: true }"
                  ></cc-select>
                </th>
                <th class="text-white text-center" style="min-width: 15%">
                  Sub Type
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let payment of allPayments">
                <ng-container
                  *ngIf="
                    !allTypeFilter || allTypeFilter.id == payment.paymentType.id
                  "
                >
                  <td class="text-center align-middle">
                    <cc-checkbox
                      [(ngModel)]="requiredPayments[payment.id]"
                      [ngModelOptions]="{ standalone: true }"
                      (ngModelChange)="onPaymentSelectionChange()"
                    ></cc-checkbox>
                  </td>
                  <td class="text-center align-middle">
                    {{ payment.dateOfPayment }}
                  </td>
                  <td class="text-center align-middle">
                    {{ payment.amount }}
                  </td>
                  <td class="text-center align-middle">
                    {{ payment.status?.label }}
                  </td>
                  <td class="text-center align-middle">
                    {{ payment.paymentType?.label }}
                  </td>
                  <td class="text-center align-middle">
                    {{ payment.subType?.label }}
                  </td>
                </ng-container>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div
        class="col-md-6"
        *ngIf="form.controls['methodOfPaymentSelect'].value === 'WIRE_TRANSFER'"
      >
        <p class="text-danger" *ngIf="showWarningMsg">
          The family paid {{ isLess ? "less" : "more" }} by {{ diff }} AED
        </p>
      </div>
      <div class="col-md-6 d-flex justify-content-end">
        <button cc-button class="me-2" (click)="cancelOperation()">
          Cancel
        </button>
        <button cc-raised-button color="primary" (click)="saveOperation()">
          Save
        </button>
      </div>
    </div>
  </form>
  <ng-template #noResultTemplate></ng-template>
</div>
