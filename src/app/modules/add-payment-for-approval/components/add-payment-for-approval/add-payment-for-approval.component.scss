// Ensure the add-for-approval breadcrumbs are visible
:host ::ng-deep .breadcrumbs-container.add-for-approval-breadcrumbs {
  display: block !important;
}

// Form styling
.form-horizontal {
  label.text-danger {
    text-align: left;
    color: #92000A;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .col-form-label {
    font-weight: 500;
  }

  // Add spacing at the bottom of the form
  .mt-4 {
    margin-bottom: 2rem;
  }

  .disabled-label {
    color: gray !important;
  }
}

// Table styling
.table-responsive {
  position: relative;
  max-height: 600px;
  overflow-y: auto;

  table {
    margin-bottom: 0;
    border-top: 2px solid #dee2e6;
    border-bottom: 2px solid #dee2e6;
  }

  thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  th {
    position: sticky;
    top: 0;
    background-color: var(--cc-secondary-color);
  }
}

// Table heading styling
h4 {
  font-weight: 500;
  padding: 0.5rem 0;
  margin-bottom: 1rem;
}
::ng-deep.custom-sel .mat-form-field-label {

  color: white !important;
}
