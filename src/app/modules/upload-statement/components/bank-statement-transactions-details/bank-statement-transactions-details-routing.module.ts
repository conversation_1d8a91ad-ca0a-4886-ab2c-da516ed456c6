import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChequeDepositsComponent } from '../cheque-deposits/cheque-deposits.component';
import { CreditCardPaymentsComponent } from '../credit-card-payments/credit-card-payments.component';
import { DirectDebitsComponent } from '../direct-debits/direct-debits.component';
import { ExpensesComponent } from '../expenses/expenses.component';
import { PdcPaymentsComponent } from '../pdc-payments/pdc-payments.component';
import { PostMoneyTransferComponent } from '../post-money-transfer/post-money-transfer.component';
import { SummaryComponent } from '../summary/summary.component';
import { UndefinedTransactionsComponent } from '../undefined-transactions/undefined-transactions.component';
import { WireTransferComponent } from '../wire-transfer/wire-transfer.component';
import { BankStatementTransactionsDetailsComponent } from './bank-statement-transactions-details.component';
import { PayrollTransfersComponent } from '../payroll-transfers/payroll-transfers.component';

const routes: Routes = [
  {
    path: '',
    component: BankStatementTransactionsDetailsComponent,
    children: [
      { path: '', redirectTo: 'direct-debits/:id', pathMatch: 'full' },
      {
        path: 'direct-debits/:id',
        component: DirectDebitsComponent,
        data: {
          label: 'Direct Debits',
          pageCode: 'BankStatementdirect-debits',
        },
      },
      {
        path: 'cheque-deposits/:id',
        component: ChequeDepositsComponent,
        data: {
          label: 'Cheque Deposits',
          pageCode: 'BankStatementcheque-deposits',
        },
      },
      {
        path: 'credit-card-payments/:id',
        component: CreditCardPaymentsComponent,
        data: {
          label: 'Credit Card Payments',
          pageCode: 'BankStatementcredit-card-payments',
        },
      },
      {
        path: 'wire-transfer/:id',
        component: WireTransferComponent,
        data: {
          label: 'Cash Deposits - Wire Transfer',
          pageCode: 'BankStatementwire-transfer',
        },
      },
      {
        path: 'pdc-payments/:id',
        component: PdcPaymentsComponent,
        data: { label: 'PDC payments', pageCode: 'BankStatementpdc-payments' },
      },
      {
        path: 'expenses/:id',
        component: ExpensesComponent,
        data: { label: 'Expenses', pageCode: 'BankStatement-expenses' },
      },
      {
        path: 'post-money-transfer/:id',
        component: PostMoneyTransferComponent,
        data: {
          label: 'Post Money Transfer',
          pageCode: 'BankStatement-expenses',
        },
      },
      {
        path: 'payroll-transfers/:id',
        component: PayrollTransfersComponent,
        data: {
          label: 'Payroll Transfers',
          pageCode: 'BankStatementPayrollTransfers',
        },
      },
      {
        path: 'undefined-transactions/:id',
        component: UndefinedTransactionsComponent,
        data: {
          label: 'Undefined Transactions',
          pageCode: 'BankStatementundefined-transactions',
        },
      },
      {
        path: 'summary/:id',
        component: SummaryComponent,
        data: { label: 'Summary', pageCode: 'BankStatement-summary' },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BankStatementTransactionsDetailsRoutingModule {}
