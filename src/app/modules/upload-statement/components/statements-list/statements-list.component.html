<div class="acc-8152">
  <div class="row bg mx-2">
    <span class="upload">Upload New Statements</span>
  </div>
  <div
    [formGroup]="uploadForm"
    class="row mx-2 align-items-center justify-content-between"
  >
    <div class="col-4">
      <cc-file-uploader
        label="Upload Bank Statement
      "
        formControlName="bankStatement"
        tag="bankStatement"
        [dropzoneConfig]="config"
      ></cc-file-uploader>
    </div>
    <div class="col-4">
      <cc-file-uploader
        label="Upload Credit Card Statement"
        formControlName="creditCardStatement"
        tag="creditCardStatement"
        [dropzoneConfig]="config"
      ></cc-file-uploader>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        (click)="proccess()"
        [disabled]="
          uploadForm.controls['creditCardStatement'].value.length == 0 &&
          uploadForm.controls['bankStatement'].value.length == 0
        "
      >
        Proccess Transactions
      </button>
    </div>
  </div>
  <div class="row bg mx-2">
    <span class="state">Statement History</span>
  </div>
  <div
    [formGroup]="filterForm"
    class="row mx-2 align-items-center justify-content-around"
  >
    <div class="col-md-auto">
      <cc-datepicker
        formControlName="uploadedFromDate"
        label="Uploaded From Date
    "
      ></cc-datepicker>
    </div>
    <div class="col-md-auto">
      <cc-datepicker
        formControlName="uploadedToDate"
        label="Uploaded To Date
  "
      ></cc-datepicker>
    </div>
    <div class="col-md-auto">
      <cc-checkbox formControlName="showDeletedFiles"
        >Show Deleted Files</cc-checkbox
      >
    </div>
    <div class="col-md-auto mb-2">
      <button cc-raised-button (click)="getFileStatement()">
        Apply Filter
      </button>
    </div>
  </div>
  <cc-datagrid
    *ngIf="records$ | async as records"
    [data]="records.content"
    [columns]="gridCols"
    [length]="records.totalElements"
    [pageOnFront]="false"
    [pageIndex]="records.number"
    [pageSize]="records.size"
    [pageSizeOptions]="[10, 25, 50]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [noResultTemplate]="noResult"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of records.content; row as row"
      [renderedActionsCount]="3"
      style="width: fit-content; gap: 8px"
    >
      <button
        *cc-action
        cc-raised-button
        color="primary"
        (click)="results(row.id)"
      >
        Results
      </button>
      <button
        *cc-action
        cc-raised-button
        color="accent"
        (click)="downloadAttachment(row)"
      >
        Download Statements
      </button>
      <button *cc-action cc-raised-button (click)="delete(row.id)">
        Delete
      </button>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #noResult> </ng-template>
</div>
