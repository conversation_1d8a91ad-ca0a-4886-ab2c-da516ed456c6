import { environment } from "./environment";

export const API = {
  base: [environment.apiBase].join('/'),
  adminMeta: [environment.apiBase, 'admin', 'sms', 'meta', 'page'].join('/'),
  public: [environment.apiBase, 'public'].join('/'),
  upload: [environment.apiBase, 'public', 'upload'].join('/'),
  download: [environment.apiBase, 'public', 'download'].join('/'),
  login: [environment.apiBase, 'public', 'login'].join('/'),
  //#region add payment for approval
  APFAtypeOfPayment: ['public','picklist', 'items', 'typeOfPayment'].join('/'),
  confirmCreate: ['accounting','contract-payment-confirmation','erp','create'].join('/'),
  APFAgetParameters:['public','parameter'].join('/'),
  getAccountingClientenChanterTodoExtraInfo:['sales','clientenchantertodo','getaccountingclientenchantertodoextrainfo'].join('/'),
  getInitialProratedNumbers:(id:any)=>['accounting','contract','getIntialandProratedAllowedPayments',id].join('/'),
  getAllowedInitialProrated:(id:any)=>['accounting','contract-payment-confirmation','AddPaymentForApprovalInfo',id].join('/'),
  APFApaymentSubType:['public','picklist','items','payment_sub_type'].join('/'),
  getPaymentDiscountInfo:(contractId:any,paymentTypeId:any)=>['accounting','payments','getPaymentDiscountInfo',contractId,paymentTypeId].join('/'),
  APFAgetunreplacedbouncedpayments:(id:any)=>['clientmgmt','contract','getunreplacedbouncedpayments',id].join('/'),
  getPayingViaCreditCardPayment:(id:any)=>['accounting','contract-payment-confirmation','getPayingViaCreditCardPayment',id].join('/'),
  getContractDetails:(id:any)=>['clientmgmt','contract',id].join('/'),
  APFAgetPaymentDetails:(replaceOf:any)=>['accounting','payments',replaceOf].join('/'),
  APFAgetClient:(id:any)=>['clientmgmt','client',id].join('/'),
  validateCreateToDoFromErp:['accounting','contract-payment-confirmation','validateCreateToDoFromErp'].join('/'),
  setReactivationPaymentForTodo:(id:any)=>['accounting','contract-payment-confirmation','setReactivationPaymentForTodo',id].join('/'),
  sendPayTabsLinkViaMessage:['accounting','contract-payment-confirmation','sendPayTabsLinkViaMessage'].join('/'),
  checkMatchedToDoIfRelatedToRunningFlow:(id:any)=>['accounting','contract-payment-confirmation','checkMatchedToDoIfRelatedToRunningFlow',id].join('/'),
  proceedStopRelatedFlowAndCreateToDo:(todoId:any,flowId:any)=>['accounting','contract-payment-confirmation','proceedStopRelatedFlowAndCreateToDo',todoId,flowId].join('/'),
  APFAgetFuturePayments:['accounting','payments','search'].join('/'),
  checkIfUserForgetToMarkPaymentAsProrated:(contractId:any)=>['accounting','contract-payment-confirmation','checkIfUserForgetToMarkPaymentAsProrated',contractId].join('/'),
  allowAddDiscountToPaymentForApproval:['accounting','contract-payment-confirmation','allowAddDiscountToPaymentForApproval'].join('/'),
  saveMonthlyOperation:['accounting','contract-payment-confirmation','saveMonthlyOperation'].join('/'),
  triggerReminderFlow:['accounting','contract-payment-confirmation','triggerReminderFlow'].join('/'),
  checkForExistingTokenizedPayments:['accounting','contract-payment-confirmation','checkForExistingTokenizedPayments'].join('/'),
    //#endregion
}
